"""
BattleCoordinatorService - 戰鬥協調服務

負責PVE戰鬥的準備、啟動、管理和結束。
這是連接戰鬥系統核心邏輯和用戶界面的關鍵服務。
"""

from typing import Dict, List, Optional, Tuple, Any, TYPE_CHECKING
import logging
import random
from enum import Enum

if TYPE_CHECKING:
    from rpg_system.config.loader import ConfigLoader
    from rpg_system.battle_system.services.attribute_calculator import AttributeCalculator
    from rpg_system.battle_system.handlers.effect_applier import EffectApplier
    from rpg_system.battle_system.handlers.target_selector import TargetSelector
    from rpg_system.battle_system.handlers.passive_trigger_handler import PassiveTriggerHandler
    from rpg_system.formula_engine.evaluator import FormulaEvaluator
    from rpg_system.repositories.player_collection_repository import PlayerCollectionRpgRepository
    from rpg_system.battle_system.models.battle import Battle, BattleStatus
    from rpg_system.battle_system.models.combatant import Combatant
    from rpg_system.battle_system.models.skill_instance import SkillInstance, SkillType

logger = logging.getLogger(__name__)


class BattleCoordinatorService:
    """
    戰鬥協調服務
    
    負責：
    1. 戰鬥準備：創建玩家隊伍和怪物隊伍
    2. 戰鬥啟動：初始化戰鬥實例
    3. 戰鬥管理：處理玩家行動和AI行動
    4. 戰鬥結束：處理結果和獎勵
    """
    
    def __init__(
        self,
        config_loader: 'ConfigLoader',
        attribute_calculator: 'AttributeCalculator',
        effect_applier: 'EffectApplier',
        target_selector: 'TargetSelector',
        passive_trigger_handler: 'PassiveTriggerHandler',
        formula_evaluator: 'FormulaEvaluator',
        player_collection_repo: 'PlayerCollectionRpgRepository',
        # user_progress_repo: 'UserProgressRPGRepository',  # TODO: 實現後添加
    ):
        self.config_loader = config_loader
        self.attribute_calculator = attribute_calculator
        self.effect_applier = effect_applier
        self.target_selector = target_selector
        self.passive_trigger_handler = passive_trigger_handler
        self.formula_evaluator = formula_evaluator
        self.player_collection_repo = player_collection_repo
        # self.user_progress_repo = user_progress_repo  # TODO: 實現後添加
        
        logger.info("BattleCoordinatorService initialized")
    
    def prepare_pve_battle(self, user_id: int, floor_id: str) -> Tuple[Optional['Battle'], Optional[str]]:
        """
        準備PVE戰鬥
        
        Args:
            user_id: 玩家ID
            floor_id: 樓層ID
            
        Returns:
            (Battle實例, 錯誤信息) 或 (None, 錯誤信息)
        """
        try:
            # 1. 獲取樓層配置
            floor_config = self.config_loader.get_floor_config(floor_id)
            if not floor_config:
                return None, f"樓層 {floor_id} 不存在"
            
            # 2. TODO: 檢查玩家進入樓層的條件
            # user_progress = self.user_progress_repo.get_user_progress(user_id)
            # if user_progress.current_floor_unlocked < int(floor_id):
            #     return None, f"樓層 {floor_id} 尚未解鎖"
            
            # 3. 隨機選擇怪物組
            if not floor_config.possible_encounters:
                return None, f"樓層 {floor_id} 沒有配置怪物"
            
            monster_group_id = random.choice(floor_config.possible_encounters)
            monster_group_config = self.config_loader.get_monster_group_config(monster_group_id)
            if not monster_group_config:
                return None, f"怪物組 {monster_group_id} 不存在"
            
            # 4. 創建玩家隊伍
            player_team, error = self._create_player_team(user_id)
            if error:
                return None, error
            
            # 5. 創建怪物隊伍
            monster_team, error = self._create_monster_team(monster_group_config)
            if error:
                return None, error
            
            # 6. 創建戰鬥實例
            from rpg_system.battle_system.models.battle import Battle
            battle = Battle(
                battle_id=f"pve_{user_id}_{floor_id}_{random.randint(1000, 9999)}",
                player_team=player_team,
                monster_team=monster_team,
                config_loader=self.config_loader,
                effect_applier=self.effect_applier,
                target_selector=self.target_selector,
                passive_trigger_handler=self.passive_trigger_handler,
                formula_evaluator=self.formula_evaluator
            )
            
            logger.info(f"戰鬥準備完成: {battle.battle_id}, 玩家隊伍: {len(player_team)}, 怪物隊伍: {len(monster_team)}")
            return battle, None
            
        except Exception as e:
            logger.error(f"準備戰鬥時發生錯誤: {e}", exc_info=True)
            return None, f"準備戰鬥失敗: {str(e)}"
    
    def start_battle(self, battle: 'Battle') -> 'Battle':
        """
        啟動戰鬥
        
        Args:
            battle: 戰鬥實例
            
        Returns:
            啟動後的戰鬥實例
        """
        try:
            battle.start()
            logger.info(f"戰鬥已啟動: {battle.battle_id}")
            return battle
        except Exception as e:
            logger.error(f"啟動戰鬥時發生錯誤: {e}", exc_info=True)
            raise
    
    def process_player_action(
        self, 
        battle: 'Battle', 
        acting_player_combatant_id: str, 
        skill_id: str, 
        target_combatant_ids: List[str]
    ) -> Tuple['Battle', Optional[str]]:
        """
        處理玩家行動
        
        Args:
            battle: 戰鬥實例
            acting_player_combatant_id: 行動的玩家戰鬥單位ID
            skill_id: 使用的技能ID
            target_combatant_ids: 目標戰鬥單位ID列表
            
        Returns:
            (更新後的Battle實例, 錯誤信息)
        """
        try:
            # 1. 驗證行動者
            caster = battle.get_combatant_by_id(acting_player_combatant_id)
            if not caster:
                return battle, f"找不到戰鬥單位: {acting_player_combatant_id}"
            
            if not caster.is_player_side:
                return battle, "只能控制玩家方戰鬥單位"
            
            # 2. 檢查是否為當前行動者
            current_actor = battle.get_acting_combatant()
            if not current_actor or current_actor.instance_id != acting_player_combatant_id:
                return battle, "不是當前行動者"
            
            # 3. 處理行動
            battle.process_action(caster, skill_id, target_combatant_ids)
            
            logger.info(f"玩家行動處理完成: {acting_player_combatant_id} 使用 {skill_id}")
            return battle, None
            
        except Exception as e:
            logger.error(f"處理玩家行動時發生錯誤: {e}", exc_info=True)
            return battle, f"處理行動失敗: {str(e)}"

    def advance_battle_turn(self, battle: 'Battle') -> 'Battle':
        """
        推進戰鬥回合，處理AI行動直到輪到玩家或戰鬥結束

        Args:
            battle: 戰鬥實例

        Returns:
            更新後的戰鬥實例
        """
        try:
            from rpg_system.battle_system.models.battle import BattleStatus

            # 循環處理AI行動直到輪到玩家或戰鬥結束
            while battle.battle_status == BattleStatus.IN_PROGRESS:
                current_actor = battle.get_acting_combatant()

                # 如果沒有行動者或輪到玩家，停止
                if not current_actor or current_actor.is_player_side:
                    break

                # AI行動
                ai_skill_id, ai_target_ids = current_actor.get_ai_action(battle, self.config_loader)
                battle.process_action(current_actor, ai_skill_id, ai_target_ids)

                # 如果戰鬥結束，停止
                if battle.battle_status != BattleStatus.IN_PROGRESS:
                    break

                # 推進到下一個行動者
                battle.next_turn()

            logger.info(f"戰鬥回合推進完成: {battle.battle_id}, 狀態: {battle.battle_status}")
            return battle

        except Exception as e:
            logger.error(f"推進戰鬥回合時發生錯誤: {e}", exc_info=True)
            raise

    def handle_pve_battle_completion(
        self,
        user_id: int,
        battle: 'Battle',
        floor_id: str
    ) -> Dict[str, Any]:
        """
        處理PVE戰鬥完成

        Args:
            user_id: 玩家ID
            battle: 戰鬥實例
            floor_id: 樓層ID

        Returns:
            包含戰鬥結果、獎勵等信息的字典
        """
        try:
            from rpg_system.battle_system.models.battle import BattleStatus

            result = {
                "battle_id": battle.battle_id,
                "battle_status": battle.battle_status.value,
                "is_victory": battle.battle_status == BattleStatus.PLAYER_WIN,
                "is_first_clear": False,
                "rewards": [],
                "floor_id": floor_id,
                "battle_log": [entry.to_dict() for entry in battle.battle_log]
            }

            # 獲取樓層配置
            floor_config = self.config_loader.get_floor_config(floor_id)
            if not floor_config:
                logger.warning(f"找不到樓層配置: {floor_id}")
                return result

            # 如果玩家勝利，處理進度和獎勵
            if battle.battle_status == BattleStatus.PLAYER_WIN:
                # TODO: 實現用戶進度更新
                # user_progress = self.user_progress_repo.get_user_progress(user_id)
                # user_progress.current_floor_wins += 1

                # TODO: 檢查是否首次通關
                # is_first_clear = user_progress.max_floor_cleared < int(floor_id)
                # result["is_first_clear"] = is_first_clear

                # TODO: 處理獎勵
                # if is_first_clear:
                #     rewards_key = floor_config.first_clear_rewards_key
                # else:
                #     rewards_key = floor_config.repeatable_rewards_per_win_key

                # if rewards_key:
                #     reward_package = self.config_loader.get_reward_package(rewards_key)
                #     if reward_package:
                #         result["rewards"] = reward_package.rewards

                # TODO: 檢查是否晉級
                # if user_progress.current_floor_wins >= floor_config.wins_required_to_advance:
                #     user_progress.current_floor_unlocked = max(
                #         user_progress.current_floor_unlocked,
                #         int(floor_id) + 1
                #     )
                #     user_progress.current_floor_wins = 0

                # self.user_progress_repo.update_user_progress(user_progress)

                logger.info(f"玩家 {user_id} 在樓層 {floor_id} 獲勝")
            else:
                logger.info(f"玩家 {user_id} 在樓層 {floor_id} 失敗")

            return result

        except Exception as e:
            logger.error(f"處理戰鬥完成時發生錯誤: {e}", exc_info=True)
            return {
                "battle_id": battle.battle_id,
                "battle_status": "ERROR",
                "is_victory": False,
                "is_first_clear": False,
                "rewards": [],
                "floor_id": floor_id,
                "error": str(e)
            }

    def _create_player_team(self, user_id: int) -> Tuple[List['Combatant'], Optional[str]]:
        """
        創建玩家隊伍

        Args:
            user_id: 玩家ID

        Returns:
            (戰鬥單位列表, 錯誤信息)
        """
        try:
            # TODO: 從用戶進度獲取隊伍配置
            # user_progress = self.user_progress_repo.get_user_progress(user_id)
            # team_formation = user_progress.current_team_formation or []

            # 暫時使用玩家的第一張卡牌作為測試
            player_cards = self.player_collection_repo.get_player_cards_by_user_id(user_id)
            if not player_cards:
                return [], "玩家沒有可用的卡牌"

            # 取第一張卡牌作為測試隊伍
            test_card = player_cards[0]

            # 獲取卡牌配置
            card_config = self.config_loader.get_card_config(test_card.card_id)
            if not card_config:
                return [], f"找不到卡牌配置: {test_card.card_id}"

            # 創建技能實例
            skill_instances = self._create_skill_instances_for_player_card(test_card, card_config)

            # 創建戰鬥單位
            from rpg_system.battle_system.models.combatant import Combatant
            combatant = Combatant(
                instance_id=f"player_{test_card.id}",
                definition_id=str(test_card.card_id),
                name=card_config.name,
                is_player_side=True,
                rpg_level=test_card.rpg_level,
                star_level=test_card.star_level,
                skill_order_preference=test_card.equipped_active_skill_ids or [],
                primary_attack_skill=skill_instances.get("primary_attack"),
                active_skills=skill_instances.get("active_skills", []),
                innate_passive=skill_instances.get("innate_passive"),
                common_passives=skill_instances.get("common_passives", []),
                position=0
            )

            # 計算屬性
            combatant.calculate_final_stats(self.config_loader, self.attribute_calculator)

            logger.info(f"創建玩家隊伍完成: 1個戰鬥單位")
            return [combatant], None

        except Exception as e:
            logger.error(f"創建玩家隊伍時發生錯誤: {e}", exc_info=True)
            return [], f"創建玩家隊伍失敗: {str(e)}"

    def _create_monster_team(self, monster_group_config) -> Tuple[List['Combatant'], Optional[str]]:
        """
        創建怪物隊伍

        Args:
            monster_group_config: 怪物組配置

        Returns:
            (戰鬥單位列表, 錯誤信息)
        """
        try:
            monster_team = []

            for i, monster_in_group in enumerate(monster_group_config.monsters):
                monster_id = monster_in_group.monster_id if hasattr(monster_in_group, 'monster_id') else monster_in_group

                # 獲取怪物配置
                monster_config = self.config_loader.get_monster_config(monster_id)
                if not monster_config:
                    return [], f"找不到怪物配置: {monster_id}"

                # 創建技能實例
                skill_instances = self._create_skill_instances_for_monster(monster_config)

                # 創建戰鬥單位
                from rpg_system.battle_system.models.combatant import Combatant
                combatant = Combatant(
                    instance_id=f"monster_{monster_id}_{i}",
                    definition_id=monster_id,
                    name=monster_config.name,
                    is_player_side=False,
                    rpg_level=getattr(monster_config, 'level', 1),
                    star_level=0,  # 怪物通常沒有星級
                    skill_order_preference=getattr(monster_config, 'active_skill_order', []),
                    primary_attack_skill=skill_instances.get("primary_attack"),
                    active_skills=skill_instances.get("active_skills", []),
                    innate_passive=skill_instances.get("innate_passive"),
                    common_passives=skill_instances.get("common_passives", []),
                    position=i
                )

                # 計算屬性
                combatant.calculate_final_stats(self.config_loader, self.attribute_calculator)
                monster_team.append(combatant)

            logger.info(f"創建怪物隊伍完成: {len(monster_team)}個戰鬥單位")
            return monster_team, None

        except Exception as e:
            logger.error(f"創建怪物隊伍時發生錯誤: {e}", exc_info=True)
            return [], f"創建怪物隊伍失敗: {str(e)}"

    def _create_skill_instances_for_player_card(self, player_card, card_config) -> Dict[str, Any]:
        """
        為玩家卡牌創建技能實例

        Args:
            player_card: 玩家卡牌數據
            card_config: 卡牌配置

        Returns:
            技能實例字典
        """
        from rpg_system.battle_system.models.skill_instance import SkillInstance, SkillType

        skill_instances = {
            "primary_attack": None,
            "active_skills": [],
            "innate_passive": None,
            "common_passives": []
        }

        try:
            # 1. 普攻技能
            if hasattr(card_config, 'primary_attack_skill_id') and card_config.primary_attack_skill_id:
                skill_instances["primary_attack"] = SkillInstance(
                    skill_id=card_config.primary_attack_skill_id,
                    skill_type=SkillType.PRIMARY_ATTACK,
                    current_level=1,  # TODO: 從全局技能等級獲取
                    current_cooldown=0
                )

            # 2. 主動技能
            if player_card.equipped_active_skill_ids:
                for skill_id in player_card.equipped_active_skill_ids:
                    if skill_id:  # 跳過空槽位
                        skill_instances["active_skills"].append(SkillInstance(
                            skill_id=skill_id,
                            skill_type=SkillType.ACTIVE,
                            current_level=1,  # TODO: 從全局技能等級獲取
                            current_cooldown=0
                        ))

            # 3. 天賦被動技能
            if hasattr(card_config, 'innate_passive_skill_id') and card_config.innate_passive_skill_id:
                skill_instances["innate_passive"] = SkillInstance(
                    skill_id=card_config.innate_passive_skill_id,
                    skill_type=SkillType.INNATE_PASSIVE,
                    current_level=player_card.star_level,  # 天賦等級基於星級
                    current_cooldown=0
                )

            # 4. 通用被動技能
            if player_card.equipped_common_passives:
                for slot_key, passive_data in player_card.equipped_common_passives.items():
                    if passive_data and isinstance(passive_data, dict):
                        skill_id = passive_data.get('skill_id')
                        if skill_id:
                            skill_instances["common_passives"].append(SkillInstance(
                                skill_id=skill_id,
                                skill_type=SkillType.PASSIVE,
                                current_level=passive_data.get('level', 1),
                                current_cooldown=0
                            ))

            return skill_instances

        except Exception as e:
            logger.error(f"創建玩家卡牌技能實例時發生錯誤: {e}", exc_info=True)
            return skill_instances

    def _create_skill_instances_for_monster(self, monster_config) -> Dict[str, Any]:
        """
        為怪物創建技能實例

        Args:
            monster_config: 怪物配置

        Returns:
            技能實例字典
        """
        from rpg_system.battle_system.models.skill_instance import SkillInstance, SkillType

        skill_instances = {
            "primary_attack": None,
            "active_skills": [],
            "innate_passive": None,
            "common_passives": []
        }

        try:
            # 1. 普攻技能
            if hasattr(monster_config, 'primary_attack_skill_id') and monster_config.primary_attack_skill_id:
                skill_instances["primary_attack"] = SkillInstance(
                    skill_id=monster_config.primary_attack_skill_id,
                    skill_type=SkillType.PRIMARY_ATTACK,
                    current_level=getattr(monster_config, 'level', 1),
                    current_cooldown=0
                )

            # 2. 主動技能
            if hasattr(monster_config, 'active_skill_order') and monster_config.active_skill_order:
                for skill_id in monster_config.active_skill_order:
                    if skill_id:
                        skill_instances["active_skills"].append(SkillInstance(
                            skill_id=skill_id,
                            skill_type=SkillType.ACTIVE,
                            current_level=getattr(monster_config, 'level', 1),
                            current_cooldown=0
                        ))

            # 3. 被動技能（怪物可能有預設的被動技能）
            if hasattr(monster_config, 'equipped_passives') and monster_config.equipped_passives:
                for passive_data in monster_config.equipped_passives:
                    if isinstance(passive_data, dict):
                        skill_id = passive_data.get('skill_id')
                        level = passive_data.get('level', 1)
                        if skill_id:
                            skill_instances["common_passives"].append(SkillInstance(
                                skill_id=skill_id,
                                skill_type=SkillType.PASSIVE,
                                current_level=level,
                                current_cooldown=0
                            ))

            return skill_instances

        except Exception as e:
            logger.error(f"創建怪物技能實例時發生錯誤: {e}", exc_info=True)
            return skill_instances
